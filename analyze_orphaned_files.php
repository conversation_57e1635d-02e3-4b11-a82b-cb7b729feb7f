<?php
/**
 * Orphaned Files Analysis Script
 * 
 * This script analyzes the codebase to identify files that are not referenced
 * and may be orphaned/unused.
 */

// Set up basic paths
$app_root = __DIR__ . '/';
$analysis_results = [];

/**
 * Get all files in the project
 */
function getAllFiles($dir, $extensions = ['php', 'edge.php', 'hilt.php', 'temp.php', 'view.php', 'api.php', 'fn.php', 'class.php']) {
    $files = [];
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS)
    );
    
    foreach ($iterator as $file) {
        if ($file->isFile()) {
            $filename = $file->getFilename();
            $path = $file->getPathname();
            
            // Check if file matches our extensions
            foreach ($extensions as $ext) {
                if (str_ends_with($filename, '.' . $ext)) {
                    $files[] = [
                        'path' => $path,
                        'relative_path' => str_replace($dir, '', $path),
                        'filename' => $filename,
                        'extension' => $ext,
                        'size' => $file->getSize(),
                        'modified' => $file->getMTime()
                    ];
                    break;
                }
            }
        }
    }
    
    return $files;
}

/**
 * Find all file references in the codebase
 */
function findFileReferences($files) {
    $references = [];
    $patterns = [
        // Direct includes/requires
        '/(?:include|require)(?:_once)?\s*\(?[\'"]([^\'"\)]+)[\'"]/',
        // Router patterns
        '/[\'"]([^\'"\s]+\.(?:view|api|edge|hilt|temp)\.php)[\'"]/',
        // File existence checks
        '/file_exists\s*\([\'"]([^\'"\)]+)[\'"]/',
        // Edge render calls
        '/Edge::render\s*\([\'"]([^\'"\)]+)[\'"]/',
        // Template references
        '/template[\'"]?\s*=>\s*[\'"]([^\'"\)]+)[\'"]/',
        // Autoloader patterns
        '/[\'"]([^\'"\s]+\.(?:class|fn)\.php)[\'"]/',
    ];
    
    foreach ($files as $file) {
        if (!file_exists($file['path'])) continue;
        
        $content = file_get_contents($file['path']);
        if ($content === false) continue;
        
        foreach ($patterns as $pattern) {
            if (preg_match_all($pattern, $content, $matches)) {
                foreach ($matches[1] as $match) {
                    if (!isset($references[$match])) {
                        $references[$match] = [];
                    }
                    $references[$match][] = $file['relative_path'];
                }
            }
        }
    }
    
    return $references;
}

/**
 * Analyze router-accessible files
 */
function getRouterAccessibleFiles() {
    $accessible = [];
    
    // Views that can be accessed via router
    $view_patterns = [
        'resources/views/**/*.view.php',
        'resources/views/**/*.edge.php',
        'system/views/**/*.view.php',
        'system/views/**/*.edge.php'
    ];
    
    // APIs that can be accessed via router
    $api_patterns = [
        'resources/views/**/*.api.php',
        'system/views/**/*.api.php',
        'system/api/**/*.api.php'
    ];
    
    // Templates that can be loaded
    $template_patterns = [
        'system/templates/**/*.hilt.php',
        'system/templates/**/*.temp.php',
        'resources/components/templates/**/*.edge.php'
    ];
    
    $patterns = array_merge($view_patterns, $api_patterns, $template_patterns);
    
    foreach ($patterns as $pattern) {
        $files = glob($pattern, GLOB_BRACE);
        foreach ($files as $file) {
            $accessible[] = str_replace(__DIR__ . '/', '', $file);
        }
    }
    
    return $accessible;
}

/**
 * Check if file is referenced by autoloader patterns
 */
function isAutoloaderAccessible($file) {
    $filename = $file['filename'];
    $path = $file['relative_path'];
    
    // Class files
    if (str_ends_with($filename, '.class.php')) {
        return true; // Autoloader can find these
    }
    
    // Function files
    if (str_ends_with($filename, '.fn.php')) {
        return true; // Autoloader can find these
    }
    
    return false;
}

/**
 * Check if file is a system/core file that should not be considered orphaned
 */
function isSystemFile($file) {
    $path = $file['relative_path'];
    $filename = $file['filename'];
    
    $system_files = [
        'index.php',
        'login.php',
        'reset-password.php',
        'webhook_autodesk_subscription.php',
        'system/autoloader.php',
        'system/paths.php',
        'system/startup_sequence.php',
        'system/startup_sequence_minimal.php',
        'system/config.php',
        'system/db_config.php',
        'system/db_config_local.php',
        'system/routes.php',
        'system/route_permissions.php',
        'system/webhook_config.php'
    ];
    
    // Check exact matches
    if (in_array($path, $system_files)) {
        return true;
    }
    
    // Check patterns
    $system_patterns = [
        '/^system\/config\//',
        '/^system\/functions\//',
        '/^resources\/components\/icons/',
        '/^vendor\//',
        '/\.htaccess$/',
        '/^cache\//',
        '/^logs\//',
        '/^uploads\//',
        '/^docs\//',
        '/^external\//'
    ];
    
    foreach ($system_patterns as $pattern) {
        if (preg_match($pattern, $path)) {
            return true;
        }
    }
    
    return false;
}

// Main analysis
echo "Starting orphaned files analysis...\n\n";

// Get all files
echo "1. Scanning all files...\n";
$all_files = getAllFiles($app_root);
echo "Found " . count($all_files) . " files\n\n";

// Find references
echo "2. Finding file references...\n";
$references = findFileReferences($all_files);
echo "Found " . count($references) . " file references\n\n";

// Get router accessible files
echo "3. Identifying router-accessible files...\n";
$router_accessible = getRouterAccessibleFiles();
echo "Found " . count($router_accessible) . " router-accessible files\n\n";

// Analyze each file
echo "4. Analyzing files for orphan status...\n";
$orphaned_files = [];
$system_files = [];
$referenced_files = [];
$autoloader_files = [];

foreach ($all_files as $file) {
    $relative_path = $file['relative_path'];
    $filename = $file['filename'];
    
    // Skip system files
    if (isSystemFile($file)) {
        $system_files[] = $file;
        continue;
    }
    
    // Check if autoloader accessible
    if (isAutoloaderAccessible($file)) {
        $autoloader_files[] = $file;
        continue;
    }
    
    // Check if directly referenced
    $is_referenced = false;
    foreach ($references as $ref_path => $ref_files) {
        if (str_contains($relative_path, $ref_path) || str_contains($ref_path, $filename)) {
            $is_referenced = true;
            break;
        }
    }
    
    if ($is_referenced) {
        $referenced_files[] = $file;
        continue;
    }
    
    // Check if router accessible
    if (in_array($relative_path, $router_accessible)) {
        continue;
    }
    
    // If we get here, the file might be orphaned
    $orphaned_files[] = $file;
}

// Output results
echo "\n" . str_repeat("=", 60) . "\n";
echo "ORPHANED FILES ANALYSIS RESULTS\n";
echo str_repeat("=", 60) . "\n\n";

echo "SUMMARY:\n";
echo "- Total files analyzed: " . count($all_files) . "\n";
echo "- System/Core files: " . count($system_files) . "\n";
echo "- Autoloader accessible: " . count($autoloader_files) . "\n";
echo "- Referenced files: " . count($referenced_files) . "\n";
echo "- Potentially orphaned: " . count($orphaned_files) . "\n\n";

if (!empty($orphaned_files)) {
    echo "POTENTIALLY ORPHANED FILES:\n";
    echo str_repeat("-", 40) . "\n";
    
    foreach ($orphaned_files as $file) {
        echo "- " . $file['relative_path'] . "\n";
        echo "  Size: " . number_format($file['size']) . " bytes\n";
        echo "  Modified: " . date('Y-m-d H:i:s', $file['modified']) . "\n\n";
    }
} else {
    echo "No orphaned files found!\n";
}

echo "\nAnalysis complete.\n";
